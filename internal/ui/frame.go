package ui

import (
	"bufio"
	"github.com/charmbracelet/bubbles/spinner"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/glamour"
	"github.com/charmbracelet/lipgloss"
	"github.com/oxio/aia/internal/paging"
	"github.com/oxio/aia/internal/terminal"
	log "github.com/sirupsen/logrus"
	"io"
	"strings"
	"sync"
)

type frameModelPaging struct {
	Overflown       bool
	OverflowContent string
}

type framePaging struct {
	currentPageIdx int
}

type plainContentContainer struct {
	val string
}

// FrameModel represents a styled frame component
type FrameModel struct {
	Content          string
	plainContent     *plainContentContainer
	lastValidContent string
	Width            int
	Height           int
	config           FrameConfig
	QuitKeys         []string
	AutoQuit         bool
	quitting         bool
	spinnerManager   *SpinnerManagerModel
	LoadingSpinner   *SpinnerWrapper
	ThinkingSpinner  *SpinnerWrapper
	isThinking       bool
	isConnecting     bool
	paging           *frameModelPaging
	pag              *framePaging
	glamourRenderer  *glamour.TermRenderer
	writer           *paging.ChunkedLineWriter
}

type contentMsg string

// NewFrame creates a new frame with default settings
func NewFrame(content string, writer *paging.ChunkedLineWriter, config FrameConfig) *FrameModel {
	// Get the terminal dimensions
	termWidth := terminal.GetWidth()
	termHeight := terminal.GetHeight()

	// Calculate default width as 80% of terminal width
	defaultWidth := int(float64(termWidth) * 0.8)

	// Ensure the width is reasonable (between 40 and 120 characters)
	if defaultWidth < 40 {
		defaultWidth = 40
	} else if defaultWidth > 120 {
		defaultWidth = 120
	}

	r, _ := glamour.NewTermRenderer(
		glamour.WithAutoStyle(),
		glamour.WithPreservedNewLines(),
		glamour.WithEmoji(),
		glamour.WithWordWrap(80), // FIXME: set actual term width
	)

	return &FrameModel{
		Content:         content,
		plainContent:    &plainContentContainer{val: content},
		Width:           defaultWidth,
		Height:          termHeight,
		config:          config,
		QuitKeys:        []string{"q", "ctrl+c", "esc"},
		AutoQuit:        false, // Auto quit by default
		spinnerManager:  NewSpinnerManagerModel(),
		LoadingSpinner:  NewLoadingSpinnerWrapper(config).SetTitle("Connecting"),
		ThinkingSpinner: NewThinkingSpinnerWrapper(config),
		isConnecting:    true,
		paging:          &frameModelPaging{},
		pag:             &framePaging{},
		glamourRenderer: r,
		writer:          writer,
	}
}

func (m FrameModel) WithWidth(width int) FrameModel {
	m.Width = width
	return m
}

// WithQuitKeys sets the keys that will quit the program
func (m FrameModel) WithQuitKeys(keys []string) FrameModel {
	m.QuitKeys = keys
	return m
}

// WithAutoQuit sets whether the frame should automatically quit
func (m FrameModel) WithAutoQuit(autoQuit bool) FrameModel {
	m.AutoQuit = autoQuit
	return m
}

func (m FrameModel) WithLoadingSpinner(spinner *SpinnerWrapper) FrameModel {
	m.LoadingSpinner = spinner
	return m
}

func (m FrameModel) WithThinkingSpinner(spinner *SpinnerWrapper) FrameModel {
	m.ThinkingSpinner = spinner
	return m
}

// Init initializes the component
func (m FrameModel) Init() tea.Cmd {
	// If AutoQuit is true, quit immediately after rendering
	if m.AutoQuit {
		return tea.Quit
	}

	mgr, cmd := m.spinnerManager.Update(m.LoadingSpinner)
	m.spinnerManager = mgr.(*SpinnerManagerModel)
	return cmd
}

// Update handles messages and updates the model
func (m FrameModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	if m.paging.Overflown {
		return m, tea.Quit
	}

	var cmds []tea.Cmd

	switch msg := msg.(type) {

	case tea.KeyMsg:
		// Check if the pressed key is in the quit keys list
		for _, key := range m.QuitKeys {
			if msg.String() == key {
				m.quitting = true
				return m, tea.Quit
			}
		}

	case tea.WindowSizeMsg:
		// TODO: handle this properly. Can it even be done? middle rendering it should be const because of pagination.
		m.Width = msg.Width
		m.Height = msg.Height

	case contentMsg:
		m.isConnecting = false
		chunks := m.writer.Chunks()
		m.Content = chunks[m.pag.currentPageIdx]

	//case contentMsg:
	//m.plainContent.val += string(msg)
	//
	//m.isConnecting = false
	//m.isThinking = false
	//
	//// Make sure we have enough content to check if the <think> tag has been opened
	//if len(m.plainContent.val) < 6 {
	//	m.Content = ""
	//	return m, nil
	//}
	//
	//// Check if we are inside an open <think> tag
	//thinkStartIdx := strings.LastIndex(m.plainContent.val, "<think>")
	//thinkEndIdx := strings.LastIndex(m.plainContent.val, "</think>")
	//// Check if <think> exists and either </think> doesn't exist or is before <think>
	//if thinkStartIdx != -1 && (thinkEndIdx == -1 || thinkEndIdx < thinkStartIdx) {
	//	m.isThinking = true
	//}
	//
	//if m.isThinking {
	//	// We are inside the <think> block, show the thinking spinner and hide content
	//	m.Content = "" // Clear content to show spinner later
	//
	//	// Switch to the thinking spinner if it's not already the active one
	//	// Note: This might generate redundant spinner commands if called repeatedly,
	//	// but the spinner manager should handle it gracefully.
	//	mgr, cmd := m.spinnerManager.Update(m.ThinkingSpinner)
	//	m.spinnerManager = mgr.(*SpinnerManagerModel)
	//	if cmd != nil {
	//		cmds = append(cmds, cmd, m.spinnerManager.GetTick())
	//	}
	//	// Return early as we don't want to render the <think> content
	//	return m, tea.Batch(cmds...)
	//}
	//
	//// If not thinking, extract the actual content to be rendered
	//// This assumes <think>...</think> blocks should not be rendered.
	//if thinkStartIdx != -1 && thinkEndIdx != -1 && thinkEndIdx > thinkStartIdx {
	//	// Remove the last completed <think>...</think> block for rendering
	//	// This is a simple approach; might need more robust parsing for complex cases.
	//	m.plainContent.val = m.plainContent.val[:thinkStartIdx] + m.plainContent.val[thinkEndIdx+len("</think>"):]
	//	// Reset lastValidContent as well if needed, although glamour handles errors.
	//}
	//
	////log.Debug("plainContent: ", m.plainContent)
	//
	//stringWriter := strings.Builder{}
	//stringWriter.Reset()
	//
	//log.Debug("U: Resetting")
	//m.writer.Reset()
	//if m.config.IsRichText() {
	//	err = m.glamourRenderer.RenderBytesTo([]byte(m.plainContent.val), m.writer) // TODO: handle error using lastValidContent\
	//} else {
	//	//_, err = m.writer.Write([]byte(m.plainContent))
	//	_, err = m.writer.Write([]byte(m.plainContent.val))
	//	_, err = stringWriter.Write([]byte(m.plainContent.val))
	//}
	//
	//if err != nil {
	//	panic(err)
	//}
	//
	//allChunks := m.writer.AllChunks()
	//
	//log.Debug("U: Written plain content: ", m.plainContent.val)
	//log.Debug("U: AllChunks: ", len(allChunks))
	//log.Debug("U: Current page: ", m.pag.currentPageIdx)
	//
	//if len(allChunks) > m.pag.currentPageIdx {
	//	//m.Content = stringWriter.String()
	//	m.Content = allChunks[m.pag.currentPageIdx]
	//}

	case FinishMsg:
		//chunks := m.writer.Chunks()
		//log.Debug("U: Finish with Chunks: ", len(chunks))
		//if len(chunks) > m.pag.currentPageIdx {
		//	m.Content = chunks[m.pag.currentPageIdx]
		//}
		return m, tea.Quit

	case spinner.TickMsg:
		mgr, cmd := m.spinnerManager.Update(msg)
		m.spinnerManager = mgr.(*SpinnerManagerModel)
		if cmd != nil {
			cmds = append(cmds, cmd)
		}
	}

	return m, tea.Batch(cmds...)
}

// View renders the component
func (m FrameModel) View() string {
	termWidth := terminal.GetWidth()
	frameWidth := m.config.MaxWidth()
	if frameWidth > (termWidth - m.config.MarginX()*2 - 1) {
		frameWidth = termWidth - m.config.MarginX()*2 - 1
	}

	frameWidthOverhead := m.config.PaddingX() * 2
	contentMaxWidth := terminal.MaxLineWidth(m.Content)

	if contentMaxWidth < (frameWidth - frameWidthOverhead) {
		frameWidth = contentMaxWidth + frameWidthOverhead
	}
	if frameWidth < 25 {
		frameWidth = 25
	}

	// Create the base frame config with content alignment
	// TODO: move this to model (except call to Width())
	frameContainerStyle := lipgloss.NewStyle().
		Margin(m.config.MarginX(), m.config.MarginY()).
		BorderStyle(m.config.Border()).
		BorderForeground(m.config.BorderColor()).
		Padding(m.config.PaddingY(), m.config.PaddingX()).
		Align(m.config.AlignH()).
		Width(frameWidth)

	var content string

	if m.isConnecting || m.isThinking {
		content = m.spinnerManager.View()
		frameContainerStyle = frameContainerStyle.BorderForeground(m.config.BorderColorLight())
		frameContainerStyle = m.spinnerManager.AdjustContainerStyle(frameContainerStyle)
		if m.isConnecting {
			frameContainerStyle = frameContainerStyle.BorderStyle(m.config.WithIcon("🔌").Border())
		}
		if m.isThinking {
			frameContainerStyle = frameContainerStyle.BorderStyle(m.config.WithIcon("🤔").Border())
		}
	} else {
		content = m.Content
	}

	renderedContent := frameContainerStyle.Render(content)

	// For right-aligned frames, add padding to the left to push it to the right
	if m.config.AlignH() == lipgloss.Right {
		// Calculate padding needed (remaining 20% of terminal width)
		padding := strings.Repeat(" ", termWidth-frameWidth-2-m.config.MarginX()*2)

		// Add padding to each line
		lines := strings.Split(renderedContent, "\n")
		for i, line := range lines {
			lines[i] = padding + line
		}

		// Rejoin the lines
		renderedContent = strings.Join(lines, "\n")
	}

	return renderedContent
}

// StreamMessage creates a frame with vibrant pink border
// reading the content from an io.Reader in a streaming fashion
func StreamMessage(reader io.Reader, style FrameConfig) {
	//writer := paging.NewChunkedLineWriter(terminal.GetHeight() - 2)
	writer := paging.NewChunkedLineWriter(2)
	frame := NewFrame("", writer, style)
	streamDistributedFrames(frame, reader)
}

func ShowMessage(content string, style FrameConfig) {
	StreamMessage(strings.NewReader(content), style)
}

func StreamAia(reader io.Reader) {
	StreamMessage(reader, AiaStyle())
}

//	func ShowAiaMessage(content string) {
//		ShowMessage(content, AiaStyle())
//	}
//
//	func StreamUserMessage(reader io.Reader) {
//		StreamMessage(reader, UserStyle())
//	}
//
//	func ShowUserMessage(content string) {
//		ShowMessage(content, UserStyle())
//	}
//
//	func StreamErrorMessage(reader io.Reader) {
//		StreamMessage(reader, ErrorStyle())
//	}
//
//	func ShowErrorMessage(content string) {
//		ShowMessage(content, ErrorStyle())
//	}
//
//	func StreamWarningMessage(reader io.Reader) {
//		StreamMessage(reader, WarningStyle())
//	}
//
//	func ShowWarningMessage(content string) {
//		ShowMessage(content, WarningStyle())
//	}
//
//	func StreamInfoMessage(reader io.Reader) {
//		StreamMessage(reader, InfoStyle())
//	}
//
//	func ShowInfoMessage(content string) {
//		ShowMessage(content, InfoStyle())
//	}
//
//	func StreamSuccessMessage(reader io.Reader) {
//		StreamMessage(reader, SuccessStyle())
//	}
//
//	func ShowSuccessMessage(content string) {
//		ShowMessage(content, SuccessStyle())
//	}

func streamDistributedFrames(frame *FrameModel, reader io.Reader) {

	var mainWg, progWg sync.WaitGroup
	var content string
	//p := tea.NewProgram(frame)
	var p *tea.Program
	bufReader := bufio.NewReader(reader)
	buf := make([]byte, 512)

	progDon := make(chan struct{})

	start := func() {
		progWg.Add(1)
		defer func() {
			progDon <- struct{}{}
		}()
		p = tea.NewProgram(frame)
		_, err := p.Run()
		if err != nil {
			panic(err)
		}
	}

	// make a chan to indicate that the new program is ready:

	mainWg.Add(1)
	go func() {
		for {
			n, err := bufReader.Read(buf)
			log.Debug("S: Read: ", string(buf[:n]))
			if n > 0 {
				content += string(buf[:n])
				_, _ = frame.writer.Write([]byte(content))
				p.Send(contentMsg(""))

				chunks := frame.writer.Chunks()
				for len(chunks) > frame.pag.currentPageIdx+1 {
					p.Send(FinishMsg{})
					p.Quit()
					progWg.Wait()
					frame.pag.currentPageIdx++
					go start()
				}
				progWg.Wait()
			}

			if err != nil {
				if err == io.EOF {
					p.Send(FinishMsg{})
					p.Quit()
					//mainWg.Done()
					break
				}
				panic(err)
			}
		}
	}()

	//go start()

	p = tea.NewProgram(frame)
	_, err := p.Run()
	if err != nil {
		panic(err)
	}

	mainWg.Wait()
}

func streamDistributedFramesOld(frame *FrameModel, reader io.Reader) {
	p := tea.NewProgram(frame)

	bufReader := bufio.NewReader(reader)

	buf := make([]byte, 512)

	var start, read func()
	var wg sync.WaitGroup

	start = func() {
		defer wg.Done()
		wg.Add(1)
		go read()
		_, err := p.Run()
		if err != nil {
			panic(err)
		}
	}

	read = func() {
		defer wg.Done()
		for {
			n, err := bufReader.Read(buf)
			if n > 0 {
				msg := string(buf[:n])

				p.Send(contentMsg(msg))

				chunks := frame.writer.Chunks()

				for frame.pag.currentPageIdx+1 < len(chunks) {
					p.Send(FinishMsg{})
					frame.pag.currentPageIdx++

					p = tea.NewProgram(frame)
					go start()
					wg.Done()
					p.Send(contentMsg(""))
				}
			}

			if err != nil {
				if err == io.EOF {
					p.Send(FinishMsg{})
					p.Send(tea.Quit())
					wg.Done()
				}
				return
			}
		}
	}

	go start()

	wg.Wait()
}
