package main

import (
	"fmt"
	"io"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// Messages
type charMsg rune
type doneMsg struct{}

// Reader that sends characters one by one
type charReader struct {
	reader io.Reader
	done   bool
}

func newCharReader(r io.Reader) *charReader {
	return &charReader{reader: r}
}

func (cr *charReader) readChar() tea.Cmd {
	return func() tea.Msg {
		buf := make([]byte, 1)
		n, err := cr.reader.Read(buf)
		if err == io.EOF || n == 0 {
			cr.done = true
			return doneMsg{}
		}
		if err != nil {
			return doneMsg{}
		}
		return charMsg(rune(buf[0]))
	}
}

// Individual program model
type programModel struct {
	id        int
	text      string
	charCount int
	reader    *charReader
	isActive  bool
	style     lipgloss.Style
}

func newProgramModel(id int, reader *charReader) programModel {
	// Create different styles for each program
	colors := []string{"#FF6B6B", "#4ECDC4", "#45B7D1", "#F7DC6F", "#BB8FCE"}
	color := colors[id%len(colors)]

	style := lipgloss.NewStyle().
		Foreground(lipgloss.Color(color)).
		Bold(true).
		Border(lipgloss.RoundedBorder()).
		Padding(1, 2)

	return programModel{
		id:       id,
		reader:   reader,
		isActive: true,
		style:    style,
	}
}

func (m programModel) Init() tea.Cmd {
	return m.reader.readChar()
}

func (m programModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	if !m.isActive {
		return m, nil
	}

	switch msg := msg.(type) {
	case charMsg:
		m.text += string(msg)
		m.charCount++

		// Check if we've reached 50 characters
		if m.charCount >= 50 {
			m.isActive = false
			return m, func() tea.Msg { return doneMsg{} }
		}

		// Continue reading
		return m, m.reader.readChar()

	case doneMsg:
		m.isActive = false
		return m, nil

	case tea.KeyMsg:
		if msg.String() == "q" || msg.String() == "ctrl+c" {
			return m, tea.Quit
		}
	}

	return m, nil
}

func (m programModel) View() string {
	header := fmt.Sprintf("Program #%d (Characters: %d/50)", m.id, m.charCount)

	status := "Active"
	if !m.isActive {
		status = "Completed"
	}

	content := fmt.Sprintf("%s - %s\n\n%s", header, status, m.text)

	return m.style.Render(content)
}

// Main coordinator model
type coordinatorModel struct {
	programs       []programModel
	currentProgram int
	reader         *charReader
	finished       bool
}

func newCoordinatorModel(reader io.Reader) coordinatorModel {
	charReader := newCharReader(reader)
	firstProgram := newProgramModel(1, charReader)

	return coordinatorModel{
		programs:       []programModel{firstProgram},
		currentProgram: 0,
		reader:         charReader,
	}
}

func (m coordinatorModel) Init() tea.Cmd {
	return m.programs[0].Init()
}

func (m coordinatorModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.KeyMsg:
		if msg.String() == "q" || msg.String() == "ctrl+c" {
			return m, tea.Quit
		}

	case doneMsg:
		if m.reader.done {
			m.finished = true
			return m, nil
		}

		// Current program is done, create a new one
		if m.currentProgram < len(m.programs) && !m.programs[m.currentProgram].isActive {
			nextProgram := newProgramModel(len(m.programs)+1, m.reader)
			m.programs = append(m.programs, nextProgram)
			m.currentProgram++

			// Initialize the new program
			return m, nextProgram.Init()
		}
	}

	// Update the current active program
	if m.currentProgram < len(m.programs) {
		updatedProgram, cmd := m.programs[m.currentProgram].Update(msg)
		m.programs[m.currentProgram] = updatedProgram.(programModel)
		return m, cmd
	}

	return m, nil
}

func (m coordinatorModel) View() string {
	title := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#FAFAFA")).
		Background(lipgloss.Color("#7D56F4")).
		Padding(0, 1).
		Render("Bubble Tea Multi-Program Example")

	help := lipgloss.NewStyle().
		Foreground(lipgloss.Color("#626262")).
		Render("Press 'q' to quit")

	var programViews []string

	// Show last 3 programs for visibility
	start := 0
	if len(m.programs) > 3 {
		start = len(m.programs) - 3
	}

	for i := start; i < len(m.programs); i++ {
		programViews = append(programViews, m.programs[i].View())
	}

	content := lipgloss.JoinVertical(lipgloss.Left,
		title,
		"",
		strings.Join(programViews, "\n\n"),
		"",
		help,
	)

	if m.finished {
		content += "\n\nAll text has been processed!"
	}

	return content
}

func main() {
	// Example text to display
	sampleText := `Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. 
Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris 
nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in 
reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla 
pariatur. Excepteur sint occaecat cupidatat non proident, sunt in 
culpa qui officia deserunt mollit anim id est laborum.`

	// Create a reader with simulated delay for visual effect
	delayedReader := &delayedReader{
		reader: strings.NewReader(sampleText),
		delay:  50 * time.Millisecond,
	}

	// Create and run the coordinator
	p := tea.NewProgram(newCoordinatorModel(delayedReader))
	if _, err := p.Run(); err != nil {
		fmt.Printf("Error running program: %v", err)
	}
}

// Helper reader that adds delay between characters for visual effect
type delayedReader struct {
	reader io.Reader
	delay  time.Duration
}

func (dr *delayedReader) Read(p []byte) (n int, err error) {
	time.Sleep(dr.delay)
	return dr.reader.Read(p)
}
